import useInsightsMutation from '@/api/hooks/mutations/useInsightsMutation';
import useTopicsMutation from '@/api/hooks/mutations/useTopicsMutation';
import useActivities from '@/api/hooks/useActivities';
import useWordCloud from '@/api/hooks/useWordCloud';
import { useOrganisation } from '@/components/OrganisationProvider';
import { author } from '@/util/author';
import hasField from '@/util/hasField';
import { sentimentScore } from '@/util/sentiment';
import { Topic } from '@quarterback/types';
import { isDefined } from '@quarterback/util';
import { useEffect, useMemo, useState } from 'react';
import { DateRange } from 'react-day-picker';

function useChatterStats(range: DateRange) {
    const organisation = useOrganisation();

    const { data: activities = [], isLoading: activitiesLoading } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: wordCloud, isLoading: wordCloudLoading } = useWordCloud(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!,
        { chatter: true }
    );

    const { getInsight } = useInsightsMutation({
        organisation: organisation.selected?.organisation!,
        entity: organisation.selected?.entity!
    });

    const { getTopics } = useTopicsMutation({
        organisation: organisation.selected?.organisation!,
        entity: organisation.selected?.entity!
    });

    const [summary, setSummary] = useState<string | undefined>();
    const [summaryError, setSummaryError] = useState<Error | undefined>();
    const [summaryLoading, setSummaryLoading] = useState<boolean>(false);

    const [topics, setTopics] = useState<Array<Topic> | undefined>();
    const [topicsError, setTopicsError] = useState<Error | undefined>();
    const [topicsLoading, setTopicsLoading] = useState<boolean>(false);

    useEffect(() => {
        async function fetchInsight() {
            if (organisation.selected && range) {
                setSummaryLoading(true);

                const { data, error } = await getInsight({
                    organisation: organisation.selected?.organisation?.id!,
                    symbol: organisation.selected?.entity?.symbol!,
                    exchange: organisation.selected?.entity?.exchange!,
                    from: range.from!,
                    to: range.to!,
                    type: 'mixed',
                    extended: true
                });
                setSummary(data);
                setSummaryLoading(false);
                setSummaryError(error);
            }
        }

        async function fetchTopics() {
            if (organisation.selected && range) {
                setTopicsLoading(true);

                const { data, error } = await getTopics({
                    organisation: organisation.selected?.organisation?.id!,
                    symbol: organisation.selected?.entity?.symbol!,
                    exchange: organisation.selected?.entity?.exchange!,
                    from: range.from!,
                    to: range.to!
                });
                setTopics(data);
                setTopicsLoading(false);
                setTopicsError(error);
            }
        }

        Promise.all([fetchInsight(), fetchTopics()]);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [organisation.selected, range]);

    const chatterActivities = useMemo(
        () =>
            activities.filter(
                (activity) => 'isBroadcast' in activity && !activity.isBroadcast
            ),
        [activities]
    );

    const authors = useMemo(() => {
        return new Set(
            chatterActivities.map((activity) => author(activity)).filter(isDefined)
        );
    }, [chatterActivities]);

    const averageSentiment = useMemo(() => {
        const agg = [...chatterActivities].filter(hasField('sentiment')).reduce(
            (agg, activity) => ({
                count: agg.count + 1,
                sum: agg.sum + sentimentScore(activity.sentiment)
            }),
            { count: 0, sum: 0 }
        );

        return agg.count > 0 ? agg.sum / agg.count : 0;
    }, [chatterActivities]);

    return {
        organisation,
        activities: chatterActivities,
        activitiesCount: chatterActivities.length,
        people: authors,
        peopleCount: authors.size,
        newPeople: 0,
        averageSentiment: averageSentiment,
        wordCloud,
        wordCloudLoading,
        summary,
        summaryError,
        summaryLoading,
        topics,
        topicsError,
        topicsLoading,
        loading:
            activitiesLoading ||
            !chatterActivities ||
            !authors ||
            averageSentiment === undefined
    };
}

export default useChatterStats;
